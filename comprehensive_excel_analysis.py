#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面的Excel分析报告生成器
包含详细的金额分类和团伙统计
"""

import pandas as pd
import numpy as np
from collections import defaultdict, Counter
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def load_and_process_data():
    """加载和处理数据"""
    print("=== 加载和处理数据 ===")
    df = pd.read_csv('提币地址.csv')
    df['dt'] = pd.to_datetime(df['dt'], format='%Y%m%d')
    
    # 处理金额数据
    df['amount_category'] = df['currency_usdt_amount'].apply(lambda x: 
        '200U以下' if pd.notna(x) and x < 200 else 
        '200U以上' if pd.notna(x) and x >= 200 else 
        '无金额数据'
    )
    
    print(f"数据加载完成: {len(df):,} 条记录")
    return df

def identify_all_groups(df, min_users=3):
    """识别所有团伙（3人以上共享地址）"""
    print(f"=== 识别团伙（{min_users}人以上共享地址） ===")
    
    # 统计每个地址的用户数
    address_users = df.groupby('address')['member_id'].nunique().reset_index()
    address_users.columns = ['address', 'user_count']
    
    # 筛选团伙地址
    group_addresses = address_users[address_users['user_count'] >= min_users]
    print(f"发现团伙地址: {len(group_addresses)} 个")
    
    # 为每个团伙生成详细信息
    groups = []
    for idx, row in group_addresses.iterrows():
        address = row['address']
        user_count = row['user_count']
        
        # 获取该地址的所有数据
        group_data = df[df['address'] == address].copy()
        members = group_data['member_id'].unique()
        
        # 基本统计
        group_info = {
            'group_id': f'G{len(groups)+1:04d}',
            'address': address,
            'member_count': user_count,
            'members': list(members),
            'total_transactions': len(group_data),
            'currencies': list(group_data['currency'].unique()),
            'currency_count': group_data['currency'].nunique(),
            'date_start': group_data['dt'].min(),
            'date_end': group_data['dt'].max(),
            'active_days': (group_data['dt'].max() - group_data['dt'].min()).days + 1
        }
        
        # 金额统计
        amount_data = group_data.dropna(subset=['currency_usdt_amount'])
        if len(amount_data) > 0:
            group_info.update({
                'total_amount': amount_data['currency_usdt_amount'].sum(),
                'avg_amount': amount_data['currency_usdt_amount'].mean(),
                'median_amount': amount_data['currency_usdt_amount'].median(),
                'max_amount': amount_data['currency_usdt_amount'].max(),
                'min_amount': amount_data['currency_usdt_amount'].min(),
                'transactions_with_amount': len(amount_data)
            })
            
            # 金额分类统计
            below_200 = amount_data[amount_data['currency_usdt_amount'] < 200]
            above_200 = amount_data[amount_data['currency_usdt_amount'] >= 200]
            
            group_info.update({
                'below_200_count': len(below_200),
                'below_200_amount': below_200['currency_usdt_amount'].sum(),
                'above_200_count': len(above_200),
                'above_200_amount': above_200['currency_usdt_amount'].sum(),
                'below_200_ratio': len(below_200) / len(amount_data) if len(amount_data) > 0 else 0
            })
        else:
            # 无金额数据的情况
            for key in ['total_amount', 'avg_amount', 'median_amount', 'max_amount', 'min_amount', 
                       'transactions_with_amount', 'below_200_count', 'below_200_amount', 
                       'above_200_count', 'above_200_amount', 'below_200_ratio']:
                group_info[key] = 0
        
        # 成员活动分析
        member_stats = []
        for member_id in members:
            member_data = group_data[group_data['member_id'] == member_id]
            member_amount_data = member_data.dropna(subset=['currency_usdt_amount'])
            
            member_stat = {
                'member_id': member_id,
                'transaction_count': len(member_data),
                'total_amount': member_amount_data['currency_usdt_amount'].sum() if len(member_amount_data) > 0 else 0,
                'avg_amount': member_amount_data['currency_usdt_amount'].mean() if len(member_amount_data) > 0 else 0,
                'first_transaction': member_data['dt'].min(),
                'last_transaction': member_data['dt'].max(),
                'currencies': list(member_data['currency'].unique()),
                'below_200_count': len(member_amount_data[member_amount_data['currency_usdt_amount'] < 200]) if len(member_amount_data) > 0 else 0,
                'above_200_count': len(member_amount_data[member_amount_data['currency_usdt_amount'] >= 200]) if len(member_amount_data) > 0 else 0
            }
            member_stats.append(member_stat)
        
        group_info['member_details'] = member_stats
        groups.append(group_info)
    
    # 按成员数量排序
    groups.sort(key=lambda x: x['member_count'], reverse=True)
    print(f"团伙分析完成: {len(groups)} 个团伙")
    
    return groups

def create_comprehensive_excel(df, groups):
    """创建全面的Excel报告"""
    print("=== 生成全面Excel报告 ===")
    
    wb = openpyxl.Workbook()
    wb.remove(wb.active)
    
    # 1. 数据概览
    create_overview_sheet(wb, df, groups)
    
    # 2. 团伙汇总
    create_groups_summary_sheet(wb, groups)
    
    # 3. 团伙详情
    create_groups_detail_sheet(wb, groups)
    
    # 4. 成员详情
    create_members_detail_sheet(wb, groups)
    
    # 5. 金额分析
    create_amount_analysis_sheet(wb, df, groups)
    
    # 6. 币种分析
    create_currency_analysis_sheet(wb, df, groups)
    
    # 7. 时间分析
    create_time_analysis_sheet(wb, df, groups)
    
    # 8. 原始数据（前1000条）
    create_raw_data_sheet(wb, df)
    
    # 保存文件
    filename = '提币地址全面分析报告.xlsx'
    wb.save(filename)
    print(f"Excel报告已保存: {filename}")
    
    return filename

def create_overview_sheet(wb, df, groups):
    """创建数据概览工作表"""
    ws = wb.create_sheet("数据概览")
    
    # 标题
    ws['A1'] = '提币地址全面分析报告'
    ws['A1'].font = Font(size=18, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:D1')
    
    # 基本统计
    ws['A3'] = '基本数据统计'
    ws['A3'].font = Font(size=14, bold=True)
    
    basic_stats = [
        ['指标', '数值', '说明'],
        ['总交易记录', f"{len(df):,}", '所有提币记录'],
        ['唯一用户数', f"{df['member_id'].nunique():,}", '不重复的member_id'],
        ['唯一地址数', f"{df['address'].nunique():,}", '不重复的提币地址'],
        ['唯一币种数', f"{df['currency'].nunique():,}", '涉及的币种类型'],
        ['时间跨度', f"{df['dt'].min().strftime('%Y-%m-%d')} 至 {df['dt'].max().strftime('%Y-%m-%d')}", '数据时间范围'],
        ['', '', ''],
        ['团伙统计', '', ''],
        ['总团伙数', f"{len(groups):,}", '3人以上共享地址'],
        ['涉及用户数', f"{sum(g['member_count'] for g in groups):,}", '团伙中的用户总数'],
        ['涉及交易数', f"{sum(g['total_transactions'] for g in groups):,}", '团伙中的交易总数'],
        ['最大团伙规模', f"{max(g['member_count'] for g in groups) if groups else 0}", '单个团伙最多用户数'],
    ]
    
    for i, row_data in enumerate(basic_stats, 4):
        for j, value in enumerate(row_data, 1):
            cell = ws.cell(row=i, column=j, value=value)
            if i == 4 or i == 11:  # 表头
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
    
    # 金额分析
    ws['A17'] = '金额分析'
    ws['A17'].font = Font(size=14, bold=True)
    
    amount_data = df.dropna(subset=['currency_usdt_amount'])
    below_200 = amount_data[amount_data['currency_usdt_amount'] < 200]
    above_200 = amount_data[amount_data['currency_usdt_amount'] >= 200]
    
    amount_stats = [
        ['分类', '交易数量', '占比', '总金额(USDT)', '平均金额(USDT)'],
        ['200U以下', f"{len(below_200):,}", f"{len(below_200)/len(amount_data)*100:.1f}%", 
         f"{below_200['currency_usdt_amount'].sum():,.2f}", f"{below_200['currency_usdt_amount'].mean():.2f}"],
        ['200U以上', f"{len(above_200):,}", f"{len(above_200)/len(amount_data)*100:.1f}%", 
         f"{above_200['currency_usdt_amount'].sum():,.2f}", f"{above_200['currency_usdt_amount'].mean():.2f}"],
        ['无金额数据', f"{len(df) - len(amount_data):,}", f"{(len(df) - len(amount_data))/len(df)*100:.1f}%", 'N/A', 'N/A'],
        ['总计', f"{len(df):,}", '100.0%', f"{amount_data['currency_usdt_amount'].sum():,.2f}", f"{amount_data['currency_usdt_amount'].mean():.2f}"]
    ]
    
    for i, row_data in enumerate(amount_stats, 18):
        for j, value in enumerate(row_data, 1):
            cell = ws.cell(row=i, column=j, value=value)
            if i == 18 or i == 22:  # 表头和总计行
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
    
    # 调整列宽
    for col in range(1, 6):
        ws.column_dimensions[chr(64 + col)].width = 20

def create_groups_summary_sheet(wb, groups):
    """创建团伙汇总工作表"""
    ws = wb.create_sheet("团伙汇总")
    
    # 标题
    ws['A1'] = '团伙汇总信息'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:M1')
    
    # 表头
    headers = ['团伙ID', '成员数', '总交易数', '币种数', '主要币种', '活动天数', '开始日期', '结束日期',
               '总金额(USDT)', '平均金额(USDT)', '200U以下交易数', '200U以上交易数', '小额交易占比']
    
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
    
    # 填充数据
    for i, group in enumerate(groups, 4):
        ws.cell(row=i, column=1, value=group['group_id'])
        ws.cell(row=i, column=2, value=group['member_count'])
        ws.cell(row=i, column=3, value=group['total_transactions'])
        ws.cell(row=i, column=4, value=group['currency_count'])
        ws.cell(row=i, column=5, value=', '.join(group['currencies'][:3]) + ('...' if len(group['currencies']) > 3 else ''))
        ws.cell(row=i, column=6, value=group['active_days'])
        ws.cell(row=i, column=7, value=group['date_start'].strftime('%Y-%m-%d'))
        ws.cell(row=i, column=8, value=group['date_end'].strftime('%Y-%m-%d'))
        ws.cell(row=i, column=9, value=round(group['total_amount'], 2) if group['total_amount'] > 0 else 'N/A')
        ws.cell(row=i, column=10, value=round(group['avg_amount'], 2) if group['avg_amount'] > 0 else 'N/A')
        ws.cell(row=i, column=11, value=group['below_200_count'])
        ws.cell(row=i, column=12, value=group['above_200_count'])
        ws.cell(row=i, column=13, value=f"{group['below_200_ratio']*100:.1f}%" if group['below_200_ratio'] > 0 else 'N/A')
        
        # 根据团伙规模着色
        if group['member_count'] >= 100:
            for col in range(1, 14):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')
        elif group['member_count'] >= 50:
            for col in range(1, 14):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='FFF2E6', end_color='FFF2E6', fill_type='solid')
    
    # 调整列宽
    for col in range(1, 14):
        ws.column_dimensions[chr(64 + col)].width = 12

def create_groups_detail_sheet(wb, groups):
    """创建团伙详情工作表"""
    ws = wb.create_sheet("团伙详情")
    
    # 标题
    ws['A1'] = '团伙详细信息'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:P1')
    
    # 表头
    headers = ['团伙ID', '共享地址', '成员数', '成员列表(前5个)', '总交易数', '币种列表', 
               '活动时间', '总金额(USDT)', '200U以下金额', '200U以上金额', '200U以下交易数', 
               '200U以上交易数', '最大单笔', '最小单笔', '中位数金额', '平均金额']
    
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
    
    # 填充数据
    for i, group in enumerate(groups, 4):
        ws.cell(row=i, column=1, value=group['group_id'])
        ws.cell(row=i, column=2, value=group['address'][:30] + '...')
        ws.cell(row=i, column=3, value=group['member_count'])
        ws.cell(row=i, column=4, value=', '.join(group['members'][:5]) + ('...' if len(group['members']) > 5 else ''))
        ws.cell(row=i, column=5, value=group['total_transactions'])
        ws.cell(row=i, column=6, value=', '.join(group['currencies']))
        ws.cell(row=i, column=7, value=f"{group['date_start'].strftime('%Y-%m-%d')} 至 {group['date_end'].strftime('%Y-%m-%d')}")
        ws.cell(row=i, column=8, value=round(group['total_amount'], 2) if group['total_amount'] > 0 else 0)
        ws.cell(row=i, column=9, value=round(group['below_200_amount'], 2))
        ws.cell(row=i, column=10, value=round(group['above_200_amount'], 2))
        ws.cell(row=i, column=11, value=group['below_200_count'])
        ws.cell(row=i, column=12, value=group['above_200_count'])
        ws.cell(row=i, column=13, value=round(group['max_amount'], 2) if group['max_amount'] > 0 else 0)
        ws.cell(row=i, column=14, value=round(group['min_amount'], 2) if group['min_amount'] > 0 else 0)
        ws.cell(row=i, column=15, value=round(group['median_amount'], 2) if group['median_amount'] > 0 else 0)
        ws.cell(row=i, column=16, value=round(group['avg_amount'], 2) if group['avg_amount'] > 0 else 0)
    
    # 调整列宽
    for col in range(1, 17):
        ws.column_dimensions[chr(64 + col)].width = 10

def create_members_detail_sheet(wb, groups):
    """创建成员详情工作表"""
    ws = wb.create_sheet("成员详情")
    
    # 标题
    ws['A1'] = '团伙成员详细信息'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:L1')
    
    # 表头
    headers = ['团伙ID', '用户ID', '交易次数', '总金额(USDT)', '平均金额(USDT)', '首次交易', '最后交易',
               '使用币种', '200U以下交易数', '200U以上交易数', '主要行为', '风险标记']
    
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
    
    # 填充数据
    row = 4
    for group in groups:
        for member in group['member_details']:
            ws.cell(row=row, column=1, value=group['group_id'])
            ws.cell(row=row, column=2, value=member['member_id'])
            ws.cell(row=row, column=3, value=member['transaction_count'])
            ws.cell(row=row, column=4, value=round(member['total_amount'], 2))
            ws.cell(row=row, column=5, value=round(member['avg_amount'], 2))
            ws.cell(row=row, column=6, value=member['first_transaction'].strftime('%Y-%m-%d'))
            ws.cell(row=row, column=7, value=member['last_transaction'].strftime('%Y-%m-%d'))
            ws.cell(row=row, column=8, value=', '.join(member['currencies'][:3]))
            ws.cell(row=row, column=9, value=member['below_200_count'])
            ws.cell(row=row, column=10, value=member['above_200_count'])
            
            # 行为分析
            if member['below_200_count'] > member['above_200_count']:
                behavior = '小额为主'
            elif member['above_200_count'] > member['below_200_count']:
                behavior = '大额为主'
            else:
                behavior = '混合'
            ws.cell(row=row, column=11, value=behavior)
            
            # 风险标记
            risk_flags = []
            if member['transaction_count'] > 10:
                risk_flags.append('高频')
            if member['total_amount'] > 10000:
                risk_flags.append('大额')
            if len(member['currencies']) > 3:
                risk_flags.append('多币种')
            
            ws.cell(row=row, column=12, value=', '.join(risk_flags) if risk_flags else '正常')
            
            row += 1
    
    # 调整列宽
    for col in range(1, 13):
        ws.column_dimensions[chr(64 + col)].width = 12

def create_amount_analysis_sheet(wb, df, groups):
    """创建金额分析工作表"""
    ws = wb.create_sheet("金额分析")

    # 标题
    ws['A1'] = '金额分析详情'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:F1')

    # 整体金额分析
    ws['A3'] = '整体金额分布'
    ws['A3'].font = Font(size=14, bold=True)

    amount_data = df.dropna(subset=['currency_usdt_amount'])

    # 金额区间分析
    amount_ranges = [
        ('0-50U', 0, 50),
        ('50-100U', 50, 100),
        ('100-200U', 100, 200),
        ('200-500U', 200, 500),
        ('500-1000U', 500, 1000),
        ('1000-5000U', 1000, 5000),
        ('5000U以上', 5000, float('inf'))
    ]

    range_headers = ['金额区间', '交易数量', '占比', '总金额(USDT)', '平均金额(USDT)', '在团伙中的数量']
    for col, header in enumerate(range_headers, 1):
        cell = ws.cell(row=5, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # 团伙数据
    group_data = pd.concat([df[df['address'] == g['address']] for g in groups]) if groups else pd.DataFrame()
    group_amount_data = group_data.dropna(subset=['currency_usdt_amount']) if len(group_data) > 0 else pd.DataFrame()

    for i, (range_name, min_val, max_val) in enumerate(amount_ranges, 6):
        if max_val == float('inf'):
            range_data = amount_data[amount_data['currency_usdt_amount'] >= min_val]
            group_range_data = group_amount_data[group_amount_data['currency_usdt_amount'] >= min_val] if len(group_amount_data) > 0 else pd.DataFrame()
        else:
            range_data = amount_data[(amount_data['currency_usdt_amount'] >= min_val) & (amount_data['currency_usdt_amount'] < max_val)]
            group_range_data = group_amount_data[(group_amount_data['currency_usdt_amount'] >= min_val) & (group_amount_data['currency_usdt_amount'] < max_val)] if len(group_amount_data) > 0 else pd.DataFrame()

        ws.cell(row=i, column=1, value=range_name)
        ws.cell(row=i, column=2, value=len(range_data))
        ws.cell(row=i, column=3, value=f"{len(range_data)/len(amount_data)*100:.1f}%")
        ws.cell(row=i, column=4, value=f"{range_data['currency_usdt_amount'].sum():,.2f}")
        ws.cell(row=i, column=5, value=f"{range_data['currency_usdt_amount'].mean():.2f}" if len(range_data) > 0 else "0")
        ws.cell(row=i, column=6, value=len(group_range_data))

    # 团伙金额分析
    ws['A15'] = '团伙金额分析'
    ws['A15'].font = Font(size=14, bold=True)

    group_amount_headers = ['团伙规模', '团伙数量', '200U以下主导团伙', '200U以上主导团伙', '混合型团伙']
    for col, header in enumerate(group_amount_headers, 1):
        cell = ws.cell(row=17, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # 按团伙规模分析
    size_ranges = [
        ('3-10人', 3, 10),
        ('11-50人', 11, 50),
        ('51-100人', 51, 100),
        ('100人以上', 101, float('inf'))
    ]

    for i, (size_name, min_size, max_size) in enumerate(size_ranges, 18):
        if max_size == float('inf'):
            size_groups = [g for g in groups if g['member_count'] >= min_size]
        else:
            size_groups = [g for g in groups if min_size <= g['member_count'] <= max_size]

        below_200_dominant = len([g for g in size_groups if g['below_200_ratio'] > 0.6])
        above_200_dominant = len([g for g in size_groups if g['below_200_ratio'] < 0.4 and g['below_200_ratio'] > 0])
        mixed = len(size_groups) - below_200_dominant - above_200_dominant

        ws.cell(row=i, column=1, value=size_name)
        ws.cell(row=i, column=2, value=len(size_groups))
        ws.cell(row=i, column=3, value=below_200_dominant)
        ws.cell(row=i, column=4, value=above_200_dominant)
        ws.cell(row=i, column=5, value=mixed)

    # 调整列宽
    for col in range(1, 7):
        ws.column_dimensions[chr(64 + col)].width = 15

def create_currency_analysis_sheet(wb, df, groups):
    """创建币种分析工作表"""
    ws = wb.create_sheet("币种分析")

    # 标题
    ws['A1'] = '币种使用分析'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:G1')

    # 整体币种分析
    ws['A3'] = '整体币种分布'
    ws['A3'].font = Font(size=14, bold=True)

    currency_dist = df['currency'].value_counts()

    currency_headers = ['币种', '交易数量', '占比', '用户数', '地址数', '在团伙中使用次数', '团伙使用率']
    for col, header in enumerate(currency_headers, 1):
        cell = ws.cell(row=5, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # 团伙币种统计
    group_currencies = []
    for group in groups:
        group_currencies.extend(group['currencies'])
    group_currency_count = Counter(group_currencies)

    for i, (currency, count) in enumerate(currency_dist.head(20).items(), 6):
        currency_data = df[df['currency'] == currency]

        ws.cell(row=i, column=1, value=currency)
        ws.cell(row=i, column=2, value=count)
        ws.cell(row=i, column=3, value=f"{count/len(df)*100:.1f}%")
        ws.cell(row=i, column=4, value=currency_data['member_id'].nunique())
        ws.cell(row=i, column=5, value=currency_data['address'].nunique())
        ws.cell(row=i, column=6, value=group_currency_count.get(currency, 0))
        ws.cell(row=i, column=7, value=f"{group_currency_count.get(currency, 0)/len(groups)*100:.1f}%" if groups else "0%")

    # 调整列宽
    for col in range(1, 8):
        ws.column_dimensions[chr(64 + col)].width = 15

def create_time_analysis_sheet(wb, df, groups):
    """创建时间分析工作表"""
    ws = wb.create_sheet("时间分析")

    # 标题
    ws['A1'] = '时间模式分析'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:F1')

    # 日期分析
    ws['A3'] = '日期活动分析'
    ws['A3'].font = Font(size=14, bold=True)

    daily_stats = df.groupby('dt').agg({
        'id': 'count',
        'member_id': 'nunique',
        'address': 'nunique',
        'currency_usdt_amount': ['sum', 'mean']
    }).round(2)

    daily_stats.columns = ['交易数', '用户数', '地址数', '总金额', '平均金额']
    daily_stats = daily_stats.fillna(0)

    # 表头
    date_headers = ['日期', '交易数', '用户数', '地址数', '总金额(USDT)', '平均金额(USDT)']
    for col, header in enumerate(date_headers, 1):
        cell = ws.cell(row=5, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # 填充最近30天的数据
    recent_data = daily_stats.tail(30)
    for i, (date, row_data) in enumerate(recent_data.iterrows(), 6):
        ws.cell(row=i, column=1, value=date.strftime('%Y-%m-%d'))
        ws.cell(row=i, column=2, value=int(row_data['交易数']))
        ws.cell(row=i, column=3, value=int(row_data['用户数']))
        ws.cell(row=i, column=4, value=int(row_data['地址数']))
        ws.cell(row=i, column=5, value=f"{row_data['总金额']:,.2f}")
        ws.cell(row=i, column=6, value=f"{row_data['平均金额']:,.2f}")

    # 团伙时间分析
    ws['A40'] = '团伙活动时间分析'
    ws['A40'].font = Font(size=14, bold=True)

    time_headers = ['活动时长', '团伙数量', '平均成员数', '平均交易数', '主要特征']
    for col, header in enumerate(time_headers, 1):
        cell = ws.cell(row=42, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # 按活动时长分类
    time_ranges = [
        ('1天', 1, 1),
        ('2-7天', 2, 7),
        ('8-30天', 8, 30),
        ('31-90天', 31, 90),
        ('90天以上', 91, float('inf'))
    ]

    for i, (time_name, min_days, max_days) in enumerate(time_ranges, 43):
        if max_days == float('inf'):
            time_groups = [g for g in groups if g['active_days'] >= min_days]
        else:
            time_groups = [g for g in groups if min_days <= g['active_days'] <= max_days]

        if time_groups:
            avg_members = sum(g['member_count'] for g in time_groups) / len(time_groups)
            avg_transactions = sum(g['total_transactions'] for g in time_groups) / len(time_groups)

            # 特征分析
            features = []
            if avg_members > 50:
                features.append('大规模')
            if avg_transactions / avg_members > 5:
                features.append('高频')

            ws.cell(row=i, column=1, value=time_name)
            ws.cell(row=i, column=2, value=len(time_groups))
            ws.cell(row=i, column=3, value=f"{avg_members:.1f}")
            ws.cell(row=i, column=4, value=f"{avg_transactions:.1f}")
            ws.cell(row=i, column=5, value=', '.join(features) if features else '正常')
        else:
            ws.cell(row=i, column=1, value=time_name)
            for col in range(2, 6):
                ws.cell(row=i, column=col, value=0)

    # 调整列宽
    for col in range(1, 7):
        ws.column_dimensions[chr(64 + col)].width = 15

def create_raw_data_sheet(wb, df):
    """创建原始数据工作表（前1000条）"""
    ws = wb.create_sheet("原始数据样本")

    # 标题
    ws['A1'] = '原始数据样本（前1000条）'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:H1')

    # 选择关键列
    key_columns = ['id', 'member_id', 'currency', 'address', 'currency_usdt_amount', 'amount_category', 'dt', 'state']
    sample_df = df[key_columns].head(1000)

    # 表头
    for col, header in enumerate(key_columns, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # 填充数据
    for row_idx, (_, row_data) in enumerate(sample_df.iterrows(), 4):
        for col_idx, value in enumerate(row_data, 1):
            if col_idx == 7:  # 日期列
                ws.cell(row=row_idx, column=col_idx, value=value.strftime('%Y-%m-%d') if pd.notna(value) else '')
            else:
                ws.cell(row=row_idx, column=col_idx, value=str(value) if pd.notna(value) else '')

    # 调整列宽
    for col in range(1, 9):
        ws.column_dimensions[chr(64 + col)].width = 15

def main():
    """主函数"""
    print("开始生成全面的Excel分析报告...")

    # 1. 加载和处理数据
    df = load_and_process_data()

    # 2. 识别所有团伙
    groups = identify_all_groups(df, min_users=3)

    # 3. 创建Excel报告
    filename = create_comprehensive_excel(df, groups)

    # 4. 生成统计摘要
    print(f"\n=== 分析摘要 ===")
    print(f"总交易记录: {len(df):,}")
    print(f"发现团伙: {len(groups)}")
    print(f"团伙涉及用户: {sum(g['member_count'] for g in groups):,}")
    print(f"最大团伙规模: {max(g['member_count'] for g in groups) if groups else 0}")

    # 金额分析
    amount_data = df.dropna(subset=['currency_usdt_amount'])
    below_200 = len(amount_data[amount_data['currency_usdt_amount'] < 200])
    above_200 = len(amount_data[amount_data['currency_usdt_amount'] >= 200])

    print(f"\n=== 金额分析 ===")
    print(f"200U以下交易: {below_200:,} ({below_200/len(amount_data)*100:.1f}%)")
    print(f"200U以上交易: {above_200:,} ({above_200/len(amount_data)*100:.1f}%)")

    print(f"\n✅ Excel报告生成完成: {filename}")
    print("包含8个工作表:")
    print("1. 数据概览 - 整体统计信息")
    print("2. 团伙汇总 - 所有团伙基本信息")
    print("3. 团伙详情 - 团伙详细分析")
    print("4. 成员详情 - 每个成员的详细信息")
    print("5. 金额分析 - 按金额区间的详细分析")
    print("6. 币种分析 - 币种使用情况")
    print("7. 时间分析 - 时间模式分析")
    print("8. 原始数据样本 - 前1000条原始数据")

if __name__ == "__main__":
    main()
